<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Hotel extends Model
{
    const HOTELS = [
        'Makkah' => 'مكة',
        'Madinah' => 'المدينة',
        'Jeddah' => 'جدة',
        'Riyadh' => 'الرياض',
        'Thaif' => 'الطائف',
    ];

    protected $connection = 'master';

    protected $fillable = [
        'name',
        'city',

        'stars',
        'distance',

        'catering',
        'price_quad',
        'price_triple',
        'price_double',
    ];

    protected $casts = [
        'price_quad' => 'float',
        'price_triple' => 'float',
        'price_double' => 'float',
    ];

    public static function getHotels(): array
    {
        return array_combine(array_keys(self::HOTELS), array_keys(self::HOTELS));
    }

    public function hotel_package(): HasMany
    {
        return $this->hasMany(HotelPackage::class);
    }

    public function prices(): HasMany
    {
        return $this->hasMany(HotelPrice::class);
    }

    public function lowest_price(): HasOne
    {
        return $this->hasOne(HotelPrice::class)->orderBy('price');
    }

    public function highest_price(): HasOne
    {
        return $this->hasOne(HotelPrice::class)->orderByDesc('price');
    }

    public static function getCities(): array
    {
        return static::query()->select('city')->distinct()->pluck('city', 'city')->toArray();
    }
}
