<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Scripts -->
    {{-- @vite(['resources/css/app.css', 'resources/js/app.js']) --}}
    <link rel="stylesheet" href="{{ asset('dist/css/tabler.min.css?123') }}">
    <link rel="stylesheet" href="{{ asset('dist/css/tabler-vendors.min.css?123') }}">
    @livewireStyles

    <style>
        @import url('https://rsms.me/inter/inter.css');

        :root {
            --tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
        }

        body {
            font-feature-settings: "cv03", "cv04", "cv11";
        }
    </style>
</head>

<body>
    <div class="wrapper">
        <header class="navbar navbar-expand-md navbar-light d-print-none">
            <div class="container-xl">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    <a href=".">
                        <img src="{{ asset('logo-wide.png') }}" width="110" height="32" alt="{{ config('app.name') }}" class="navbar-brand-image">
                    </a>
                </h1>
                <div class="flex-row navbar-nav order-md-last">
                    <div class="nav-item dropdown">
                        <a href="#" class="p-0 nav-link d-flex lh-1 text-reset" data-bs-toggle="dropdown" aria-label="Open user menu">
                            <div class="text-end d-none d-xl-block pe-2">
                                <div>{{ auth()->user()->name }}</div>
                                <div class="mt-1 small text-muted">{{ auth()->user()->email }}</div>
                            </div>
                            <span class="avatar avatar-sm" style="background-image: url('{{ get_gravatar_url(auth()->user()->email) }}');">
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                            <a href="{{ route('profile.edit') }}" class="dropdown-item">Profile</a>
                            {{-- <div class="dropdown-divider"></div> --}}
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf

                                <a href="{{ route('logout') }}" class="dropdown-item" onclick="event.preventDefault();
                                                        this.closest('form').submit();">Logout</a>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="collapse navbar-collapse" id="navbar-menu">
                    <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                        <ul class="navbar-nav">
                            {{-- <x-nav-item :href="route('dashboard')" :active="request()->routeIs('dashboard')"
                                    icon="tabler-home">
                                    Dashboard
                                </x-nav-item> --}}
                            <x-nav-item :href="route('hotels.index')" :active="request()->routeIs('hotels.*')" icon="tabler-building-skyscraper">
                                Hotels
                            </x-nav-item>
                            <x-nav-item :href="route('packages.index')" :active="request()->routeIs('packages.*')" icon="tabler-packages">
                                Packages
                            </x-nav-item>
                        </ul>
                    </div>
                </div>
            </div>
        </header>
        <div class="page-wrapper">
            @if (isset($header))
            <div class="container-xl">
                <!-- Page title -->
                <div class="page-header d-print-none">
                    {{ $header }}
                </div>
            </div>
            @endif
            <div class="page-body">
                <div class="container-xl">
                    {{ $slot }}
                </div>
            </div>
        </div>
    </div>
    <x-livewire-modals />
    <x-toasts />
    <!-- Libs JS -->
    <script src="{{ asset('dist/libs/tom-select/dist/js/tom-select.base.min.js') }}"></script>
    <!-- Tabler Core -->
    <script src="{{ asset('dist/js/tabler.min.js?123') }}"></script>
    @livewireScripts
    @stack('scripts')
</body>

</html>
