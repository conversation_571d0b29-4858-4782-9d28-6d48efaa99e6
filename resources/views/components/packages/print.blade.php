@props(['package'])

@php
    use App\Enums\Currency;

    $prices = $package->getPriceTable(formatMoney: false);
@endphp
<section>
    <style media="print">
        /* @page {
            size: 21.5cm 33cm;
            margin: 0;
        } */

        .bg img {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -1;
        }
    </style>
    <style media="screen">
        .fi-print-preview, .document-body {
            background: url('{{ asset('images/bg-print.jpg') }}') no-repeat center center;
            background-size: cover;
        }
        .bg {
            display: none;
        }
    </style>
    <style>
        .doc-package {
            font-size: 8pt;
        }
    </style>
    <div class="doc-package">
        <div class="bg">@inlinedImage(asset('images/bg-print.jpg'))</div>
        <table class="w-full">
            <thead>
                <tr>
                    <td class="h-[1cm]"></td>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="px-[1cm]">
        <div class="flex items-start gap-[1cm]">
            <h1 class="text-primary-600 text-[32pt] leading-none font-extrabold flex-1 min-w-0">{{ str($package->title)->title()->replace(' La ', ' LA ') }}</h1>
            <img src="{{ asset('images/logo.svg') }}" alt="" class="block h-[2.5cm] w-auto">
        </div>
        @php
            $nights = $package->hotels->sum('pivot.nights');
        @endphp
        <div class="flex items-start gap-[0.5cm] mt-[5pt]">
            <div>
                @if (!$package->isHandlingOnly())
                    <p class="font-bold bg-armyellow text-white rounded-lg px-3 py-1 text-[12pt]">Program {{ $nights }} Malam</p>
                @endif
                @if (filled($package->dates))
                    <div class="text-[12pt] text-armblue font-semibold mt-[5pt]">
                        @foreach ($package->dates as $date)
                            <p class="py-1">{{ formatDateRange($date->date_start, $date->date_end) }}</p>
                        @endforeach
                    </div>
                @endif
            </div>
            @if ($package->customer)
                <div class="flex ml-auto items-start w-[8cm] border-b border-black pb-[5pt]">
                    <div class="flex-1 min-w-0">
                        <div class="text-[10pt] text-gray-500 font-semibold">Customer:</div>
                        <div class="text-[10pt] font-semibold">{{ $package->customer->name }}</div>
                    </div>
                    <div class="p-[5pt]">
                        @if ($package->customer?->logo_square || $package->customer?->logo)
                            @inlinedImage($package->customer?->logoUrl ?? $package->customer?->logoSquareUrl, 'w-auto h-[1.25cm]')
                        @endif
                    </div>
                </div>
            @endif
        </div>
        <div class="flex items-start mt-[10pt]">
            @foreach ($package->hotels as $hotel)
                <div class="flex-1 min-w-0">
                    <p class="text-gray-500 text-[11pt]">Hotel {{ $hotel->city }}</p>
                    <p class="text-armblue font-bold text-[20pt] leading-tight">{{ $hotel->name }}</p>
                    <div class="flex items-center gap-[10pt]">
                        <p class="px-3 py-1 text-[9pt] font-bold leading-tight text-white rounded-md bg-armyellow">{{ $hotel->pivot->nights }} MALAM</p>
                        <p class="text-armyellow text-[12pt]">{{ collect(range(1, $hotel->stars))->map(fn () => '★')->join('') }}</p>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="mt-[10pt] bg-white border border-gray-500 rounded-xl overflow-hidden">
            @if ($package->isHandlingOnly())
            <table class="w-full font-semibold table-fixed">
                <tr class="text-[11pt] text-left border-b border-gray-500">
                    <th class="px-[0.75cm] py-[0.25cm]" style="width: 80mm;">
                        TOTAL PAX
                    </th>
                    <th class="px-[0.75cm] py-[0.25cm] border-l border-gray-500">RATE PER PAX</th>
                </tr>
                <tr>
                    <td class="h-[0.25cm]"></td>
                    <td class="h-[0.25cm] border-l border-gray-500"></td>
                </tr>
                @foreach ($prices as $price)
                    <tr class="text-[16pt]">
                        <td class="px-[0.75cm]">
                            {{ $price['paxes'] }}
                            <span class="text-[9pt] align-super">PAX</span>
                        </td>
                        <td class="px-[0.75cm] border-l border-gray-500">{!! formatPrice($price['price_quad'], $package->currency) !!}</td>
                    </tr>
                @endforeach
                <tr>
                    <td class="h-[0.25cm]"></td>
                    <td class="h-[0.25cm] border-l border-gray-500"></td>
                </tr>
            </table>
            @else
            @php
            $hasQuad = $package->hasPrice('quad');
            $hasTriple = $package->hasPrice('triple');
            $hasDouble = $package->hasPrice('double');
            $hasSingle = $package->hasPrice('single');
            $typeCount = collect([$hasQuad, $hasTriple, $hasDouble, $hasSingle])
                ->filter()
                ->count();

            if (!$typeCount) {
                $typeCount = 3;
                $hasQuad = $hasTriple = $hasDouble = true;
            }

            $typeWidth = 106 / $typeCount;
            @endphp
            <table class="w-full font-semibold table-fixed">
                <tr class="text-[11pt] text-left border-b border-gray-500">
                    <th class="px-[0.75cm] py-[0.25cm]" style="width: 34mm;">
                        TOTAL PAX
                    </th>
                    @if ($hasQuad)
                        <th class="px-[0.75cm] py-[0.25cm] border-l border-gray-500" style="width: {{ $typeWidth }}mm;">QUAD</th>
                    @endif
                    @if ($hasTriple)
                        <th class="px-[0.75cm] py-[0.25cm] border-l border-gray-500" style="width: {{ $typeWidth }}mm;">TRIPLE</th>
                    @endif
                    @if ($hasDouble)
                        <th class="px-[0.75cm] py-[0.25cm] border-l border-gray-500" style="width: {{ $typeWidth }}mm;">DOUBLE</th>
                    @endif
                    @if ($hasSingle)
                        <th class="px-[0.75cm] py-[0.25cm] border-l border-gray-500" style="width: {{ $typeWidth }}mm;">SINGLE</th>
                    @endif
                </tr>
                <tr>
                    <td class="h-[0.25cm]"></td>
                    @if ($hasQuad)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                    @if ($hasTriple)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                    @if ($hasDouble)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                    @if ($hasSingle)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                </tr>
                @foreach ($prices as $price)
                    <tr class="text-[16pt]">
                        <td class="px-[0.75cm]">
                            {{ $price['paxes'] }}
                            <span class="text-[9pt] align-super">PAX</span>
                        </td>
                    @if ($hasQuad)
                        <td class="px-[0.75cm] border-l border-gray-500">{!! formatPrice($price['price_quad'], $package->currency) !!}</td>
                    @endif
                    @if ($hasTriple)
                        <td class="px-[0.75cm] border-l border-gray-500">{!! formatPrice($price['price_triple'], $package->currency) !!}</td>
                    @endif
                    @if ($hasDouble)
                        <td class="px-[0.75cm] border-l border-gray-500">{!! formatPrice($price['price_double'], $package->currency) !!}</th>
                    @endif
                    @if ($hasSingle)
                        <td class="px-[0.75cm] border-l border-gray-500">{!! formatPrice($price['price_single'], $package->currency) !!}</th>
                    @endif
                    </tr>
                @endforeach
                <tr>
                    <td class="h-[0.25cm]"></td>
                    @if ($hasQuad)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                    @if ($hasTriple)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                    @if ($hasDouble)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                    @if ($hasSingle)
                        <td class="h-[0.25cm] border-l border-gray-500"></td>
                    @endif
                </tr>
            </table>
            @endif
        </div>
        <div class="mt-[5pt] flex items-start justify-between">
            <div>
                @if ($package->currency == Currency::USD)
                    <p class="font-semibold text-[8pt]">KURS 1 USD = 3.75 SAR</p>
                @endif
            </div>
            <div class="text-[7pt]">
                * Harga sewaktu-waktu dapat berubah tanpa pemberitahuan sebelumnya, harap konfirmasi kembali.
                <span class="font-bold">Update: {{ $package->updated_at->format('j M Y') }}</span>
            </div>
        </div>
        <div class="grid grid-cols-3 gap-4 mt-[5pt] text-[8pt]">
            <div class="col-span-2">
                <p class="mb-1 font-bold bg-green-500 text-white text-center text-[9pt] rounded py-[4pt]">Harga Sudah Termasuk</p>
                <div class="columns-2">
                @foreach ($package->meta['includes'] as $item)
                    <p class="flex items-start gap-1">
                        <x-tabler-point-filled class="flex-shrink-0 w-2 h-2 my-[3px]" />
                        <span class="min-w-0">{{ $item }}</span>
                    </p>
                @endforeach
                </div>
            </div>
            <div>
                <p class="mb-1 font-bold bg-red-500 text-white text-center text-[9pt] rounded py-[4pt]">Harga Tidak Termasuk</p>
                @foreach ($package->meta['excludes'] as $item)
                    <p class="flex items-start gap-1">
                        <x-tabler-point-filled class="flex-shrink-0 w-2 h-2 my-[3px]" />
                        <span class="min-w-0">{{ $item }}</span>
                    </p>
                @endforeach
            </div>
        </div>
        @if (filled($package->meta['notes'] ?? []))
            <div class="border-t border-armblue mt-[5pt] mb-[7pt]"></div>
            <div class="px-3 py-2 gap-[0.5cm] bg-white rounded-lg border-2 border-gray-400 text-[8pt] flex items-start">
                <p class="flex-shrink-0 mb-1 font-semibold text-gray-500">Catatan</p>
                <div class="flex-1 min-w-0 columns-3">
                @foreach ($package->meta['notes'] as $item)
                    <p class="flex items-start gap-1">
                        <x-tabler-point-filled class="flex-shrink-0 w-2 h-2 my-[3px]" />
                        <span class="min-w-0">{{ $item }}</span>
                    </p>
                @endforeach
                </div>
            </div>
        @endif
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td class="pt-[0.5cm]">
                        <div class="hidden print:block print:h-[2.75cm]">&nbsp;</div>
                        <div class="bg-armblue text-white h-[2.75cm] flex items-center gap-[1cm] px-[1cm] border-t-4 border-armyellow justify-between print:fixed print:left-0 print:right-0 print:bottom-0">
                            <div class="flex text-[11pt] flex-col items-start font-bold leading-none tracking-tight">
                                <span>Pelopor Layanan</span>
                                <span class="text-armyellow">Land Arrangement</span>
                                <span>Umrah &amp; Haji</span>
                            </div>
                            <div class="flex-1 min-w-0 text-[11pt] font-bold">www.umrahservice.co</div>
                            <div class="px-4 py-2 leading-tight text-center bg-white rounded-lg text-armblue">
                                <p class="font-bold text-[8pt] text-gray-500">Informasi &amp; Pemesanan</p>
                                <p class="font-bold text-[14pt]">+62 812-8955-2018</p>
                                <p class="font-semibold text-[8pt]">M. FARUQ AL ISLAM</p>
                            </div>
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</section>
