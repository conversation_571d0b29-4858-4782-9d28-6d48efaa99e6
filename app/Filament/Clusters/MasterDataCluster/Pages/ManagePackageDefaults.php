<?php

namespace App\Filament\Clusters\MasterDataCluster\Pages;

use App\Filament\Clusters\MasterDataCluster;
use App\Settings\PackageDefaults;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class ManagePackageDefaults extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $settings = PackageDefaults::class;

    protected static ?string $cluster = MasterDataCluster::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // ...
            ]);
    }
}
