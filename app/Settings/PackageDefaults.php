<?php

namespace App\Settings;

use App\Enums\Currency;
use <PERSON><PERSON>\LaravelSettings\Settings;

class PackageDefaults extends Settings
{
    public int $visa;

    public int $transport;

    public int $transport_min;

    public int $mutawif;

    public array $costs;

    public array $costs_per_pax;

    public int $margin;

    public float $usd_rate;

    public int $foc_count;

    public Currency $currency;

    public float $exchange_rate;

    public array $meta;

    public static function group(): string
    {
        return 'defaults';
    }

    public static function getDefaults(): array
    {
        return [
            'visa' => 550,
            'transport' => 100,
            'transport_min' => 3000,
            'mutawif' => 250,
            'costs' => [
                ['check' => 1, 'name' => 'Baksis Supir', 'value' => 500],
                ['check' => 1, 'name' => 'Baksis Bellboy', 'value' => 500],
                ['check' => 1, 'name' => 'Operasional', 'value' => 1000],
                ['check' => 1, 'name' => 'Mutawifah', 'value' => 200],
                ['check' => 1, 'name' => 'Bus Stasiun', 'value' => 1000],
                ['check' => 1, 'name' => 'Bus Thaif', 'value' => 1000],
                ['check' => 1, 'name' => 'Bus Badar', 'value' => 1500],
                ['check' => 1, 'name' => 'Bus Al-Ula', 'value' => 3000],
                ['check' => 1, 'name' => 'Uang Saku TL', 'value' => 1000],
                ['check' => 1, 'name' => 'Fee TL', 'value' => 1000],
            ],
            'costs_per_pax' => [
                ['check' => 1, 'name' => 'Fee Agen', 'value' => 50],
                ['check' => 1, 'name' => 'Margin Paket Umrah', 'value' => 0],
                ['check' => 1, 'name' => 'Tiket Pesawat', 'value' => 0],
                ['check' => 1, 'name' => 'Tiket Kereta', 'value' => 250],
                ['check' => 1, 'name' => 'Tiket Telefric', 'value' => 100],
                ['check' => 1, 'name' => 'Tiket Toboggan', 'value' => 50],
                ['check' => 1, 'name' => 'Makan Siang Tour', 'value' => 25],
                ['check' => 1, 'name' => 'Snack 3 Trip', 'value' => 20],
                ['check' => 1, 'name' => 'Meal Box PP', 'value' => 40],
                ['check' => 1, 'name' => 'Zamzam 5 ltr', 'value' => 15],
                ['check' => 1, 'name' => 'Handling Bandara PP', 'value' => 80],
                ['check' => 1, 'name' => 'Fee Operator', 'value' => 30],
                ['check' => 1, 'name' => 'Handling Bandara Indonesia', 'value' => 25],
                ['check' => 1, 'name' => 'Lounge Zukavia', 'value' => 50],
                ['check' => 1, 'name' => 'Manasik', 'value' => 50],
                ['check' => 1, 'name' => 'Perlengkapan Umrah', 'value' => 250],
                ['check' => 1, 'name' => 'Siskopatuh', 'value' => 135],
            ],
            'margin' => 3000,
            'usd_rate' => 3.75,
            'foc_count' => 0,
            'currency' => Currency::USD,
            'exchange_rate' => 1.0,
            'meta' => [
                'paxes' => [44, 40, 36, 32, 28, 24, 20, 16, 12],
                'includes' => [
                    'Visa umrah & asuransi kesehatan Arab Saudi.',
                    'Akomodasi hotel Madinah & Makkah sesuai program.',
                    'Transportasi selama di Arab Saudi.',
                    'Transportasi ziarah Makkah 1 x dan Madinah 1x.',
                    'Muthawwif atau pembimbing ibadah berbahasa Indonesia.',
                    'Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat.',
                    'Makan & minum 3x sehari.',
                    'Snack premium.',
                    'Handling kedatangan dan kepulangan di bandara.',
                    'Meal box kedatangan dan kepulangan di bandara.',
                    'Handling check-in dan check-out hotel.',
                    'Tips porter bandara dan bellboy hotel.',
                    "Distribusi koper ke kamar jama'ah.",
                    "Air mineral di setiap kamar jama'ah.",
                    'Free air zamzam 5lt.',
                ],
                'excludes' => [
                    'Tiket pesawat.',
                    'Asuransi perjalanan.',
                    'Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program.',
                    'Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah.',
                ],
            ],
        ];
    }
}
