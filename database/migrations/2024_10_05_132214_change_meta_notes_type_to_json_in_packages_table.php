<?php

use App\Models\Package;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Package::query()
            ->get()
            ->each(function ($package) {
                $meta = $package->meta;
                if (isset($meta['notes'])) {
                    $package->update([
                        'meta' => [
                            ...$meta,
                            'notes' => json_encode(explode('\n', $meta['notes'])),
                        ],
                    ]);
                }
            });

        Schema::table('packages', function (Blueprint $table) {
            //
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            //
        });
    }
};
