import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
    content: ['./resources/**/*.blade.php'],
    darkMode: 'class',
    theme: {
        extend: {
            colors: {
                armblue: "rgba(46, 69, 162, <alpha-value>)",
                armyellow: "rgba(199, 163, 79, <alpha-value>)",
            },
            fontFamily: {
                sans: ['Inter', ...defaultTheme.fontFamily.sans],
            },
        },
    },
    plugins: [forms, typography],
};
