<?php

namespace App\Filament\Clusters\MasterDataCluster\Resources\HotelResource\Pages;

use App\Enums\RoomType;
use App\Filament\Clusters\MasterDataCluster\Resources\HotelResource;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Table;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class Prices extends ManageRelatedRecords
{
    protected static string $resource = HotelResource::class;

    protected static string $relationship = 'prices';

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function getNavigationLabel(): string
    {
        return 'Prices';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->live()
                    ->afterStateUpdated(function ($state, Set $set) {
                        $dates = explode(' - ', $state);

                        if (count($dates) == 2) {
                            $set('date_start', Carbon::createFromFormat('d/m/Y', $dates[0])->toDateString());
                            $set('date_end', Carbon::createFromFormat('d/m/Y', $dates[1])->toDateString());
                        }
                    })
                    ->required(),
                Forms\Components\Hidden::make('date_start'),
                Forms\Components\Hidden::make('date_end'),
                Forms\Components\ToggleButtons::make('room_type')
                    ->options(RoomType::class)
                    ->inline()
                    ->required(),
                Forms\Components\TextInput::make('price')
                    ->mask(RawJs::make('$money($input)'))
                    ->stripCharacters(',')
                    ->numeric()
                    ->prefix('SAR')
                    ->required(),
            ])
            ->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('date_start')
                    ->label('Date range')
                    ->getStateUsing(fn ($record) => $record->date_start->format('d-M-y').' - '.$record->date_end->format('d-M-y'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('room_type')
                    ->badge()
                    ->color('gray')
                    ->sortable(),
                Tables\Columns\TextColumn::make('price')
                    ->money('SAR')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('bulk_add')
                    ->label('Bulk add prices')
                    ->color('gray')
                    ->form([
                        DateRangePicker::make('date_range')
                            ->required(),
                        TableRepeater::make('prices')
                            ->reorderable(false)
                            ->headers([
                                Header::make('Room type')
                                    ->markAsRequired(),
                                Header::make('Price')
                                    ->markAsRequired(),
                            ])
                            ->schema([
                                Forms\Components\Select::make('room_type')
                                    ->options(RoomType::class)
                                    ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                    ->required(),
                                Forms\Components\TextInput::make('price')
                                    ->mask(RawJs::make('$money($input)'))
                                    ->stripCharacters(',')
                                    ->numeric()
                                    ->prefix('SAR')
                                    ->required(),
                            ])
                            ->minItems(1)
                            ->columns(2)
                            ->default(collect(RoomType::cases())
                                ->map(fn ($t) => ['room_type' => $t->value])
                                ->toArray()),
                    ])
                    ->action(function ($data) {
                        $dates = explode(' - ', $data['date_range']);

                        $date_start = Carbon::createFromFormat('d/m/Y', $dates[0])->toDateString();
                        $date_end = Carbon::createFromFormat('d/m/Y', $dates[1])->toDateString();

                        collect($data['prices'])
                            ->each(fn ($p) => $this->getOwnerRecord()->prices()->create([
                                'date_start' => $date_start,
                                'date_end' => $date_end,
                                'room_type' => $p['room_type'],
                                'price' => $p['price'],
                            ]));
                    })
                    ->modalSubmitActionLabel('Create')
                    ->modalWidth('lg'),
                Tables\Actions\CreateAction::make()
                    ->label('Add price')
                    ->modalWidth('md'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($record, $data) {
                        $date_start = $record->date_start->format('d/m/Y');
                        $date_end = $record->date_end->format('d/m/Y');
                        $data['date_range'] = "$date_start - $date_end";

                        return $data;
                    })
                    ->modalWidth('md'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
