<?php

namespace App\Filament\Clusters\MasterDataCluster\Resources;

use App\Filament\Clusters\MasterDataCluster;
use App\Filament\Clusters\MasterDataCluster\Resources\CustomerResource\Pages;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\Infolists\PhoneEntry;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $cluster = MasterDataCluster::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?int $navigationSort = 0;

    protected static ?string $recordTitleAttribute = 'name';

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'owner_name', 'email', 'phone'];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                InfoLists\Components\Section::make()
                    ->schema([
                        InfoLists\Components\TextEntry::make('name')
                            ->label('Company name'),
                        InfoLists\Components\TextEntry::make('owner_name'),
                        InfoLists\Components\TextEntry::make('email'),
                        PhoneEntry::make('phone'),
                        InfoLists\Components\TextEntry::make('permit_no')
                            ->label('No. Izin PPIU'),
                        InfoLists\Components\TextEntry::make('region')
                            ->label('Domisili'),
                        InfoLists\Components\TextEntry::make('address'),
                    ])
                    ->columns(['lg' => 2])
                    ->columnSpan(['lg' => 2]),
                InfoLists\Components\Section::make()
                    ->schema([
                        InfoLists\Components\ImageEntry::make('logoUrl')
                            ->label('Logo')
                            ->width('100%')
                            ->height('auto'),
                        InfoLists\Components\ImageEntry::make('logoSquareUrl')
                            ->label('Square logo'),
                    ])
                    ->columnSpan(1),
            ])
            ->columns(['lg' => 3]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Company name')
                            ->required(),
                        Forms\Components\TextInput::make('owner_name'),
                        Forms\Components\TextInput::make('email')
                            ->email(),
                        PhoneInput::make('phone'),
                        Forms\Components\TextInput::make('permit_no')
                            ->label('No. Izin PPIU'),
                        Forms\Components\TextInput::make('region')
                            ->label('Domisili'),
                        Forms\Components\Textarea::make('address')
                            ->rows(2),
                        Forms\Components\FileUpload::make('logo')
                            ->image()
                            ->disk('s3')
                            ->directory('customers')
                            ->visibility('public'),
                        Forms\Components\FileUpload::make('logo_square')
                            ->label('Square logo')
                            ->image()
                            ->disk('s3')
                            ->directory('customers')
                            ->visibility('public'),
                    ])
                    ->columns(['md' => 2])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('logo')
                    ->label('')
                    ->getStateUsing(fn ($record) => $record->logoSquareUrl ?? $record->logoUrl),
                Tables\Columns\TextColumn::make('name')
                    ->label('Company')
                    ->description(fn ($record) => $record->email)
                    ->sortable()
                    ->searchable(['customers.name', 'customers.email']),
                Tables\Columns\TextColumn::make('owner_name')
                    ->label('Owner')
                    ->searchable(),
                PhoneColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'view' => Pages\ViewCustomer::route('/{record}'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
