{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "repositories": [{"type": "vcs", "url": "**************:arrahmah/umrahservice-core-module.git"}], "require": {"php": "^8.2", "akaunting/laravel-money": "^5.1", "arrahmah/umrahservice-core-module": "dev-main", "awcodes/filament-table-repeater": "^3.0", "awcodes/shout": "^2.0", "fauzie811/filament-list-entry": "^1.0", "filament/filament": "^3.2", "geniusts/hijri-dates": "^1.1", "guava/filament-clusters": "^1.4", "guzzlehttp/guzzle": "^7.2", "icetalker/filament-table-repeatable-entry": "^1.0", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.29", "livewire/livewire": "^3.4", "lorisleiva/laravel-actions": "^2.7", "malzariey/filament-daterangepicker-filter": "^2.8", "ncjoes/office-converter": "^1.0", "phpoffice/phpspreadsheet": "^2.0", "phpoffice/phpword": "^1.0", "ryangjchandler/blade-tabler-icons": "^2.0", "spatie/eloquent-sortable": "^4.4", "spatie/laravel-permission": "^6.4", "spatie/laravel-settings": "^3.4", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/breeze": "^2.0", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}}