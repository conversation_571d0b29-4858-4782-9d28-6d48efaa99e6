<?php

namespace App\Filament\Clusters\MasterDataCluster\Resources\HotelResource\Pages;

use App\Filament\Clusters\MasterDataCluster\Resources\HotelResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ViewHotel extends ViewRecord
{
    protected static string $resource = HotelResource::class;

    protected static ?string $navigationLabel = 'Details';

    protected static ?string $navigationIcon = 'heroicon-o-bars-3-bottom-left';

    protected static ?string $breadcrumb = 'Details';

    public function getTitle(): string|Htmlable
    {
        return $this->record->fullname;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
