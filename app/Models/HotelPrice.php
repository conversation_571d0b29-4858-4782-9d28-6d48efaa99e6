<?php

namespace App\Models;

use App\Enums\RoomType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class HotelPrice extends Model
{
    protected $connection = 'mysql';

    protected $fillable = [
        'hotel_id',

        'date_start',
        'date_end',
        'room_type',
        'price',
    ];

    protected $casts = [
        'date_start' => 'date',
        'date_end' => 'date',
        'room_type' => RoomType::class,
        'price' => 'float',
    ];

    public function hotel(): BelongsTo
    {
        return $this->belongsTo(Hotel::class);
    }
}
