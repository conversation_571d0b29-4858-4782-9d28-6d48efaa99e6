<?php

namespace App\Console\Commands;

use App\Models\Package;
use Illuminate\Console\Command;

class MigrateDates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-dates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Package::all()
            ->each(function ($package) {
                if ($package->date_start && $package->date_end) {
                    $package->dates()->create([
                        'date_start' => $package->date_start,
                        'date_end' => $package->date_end,
                    ]);
                }
            });

        return Command::SUCCESS;
    }
}
