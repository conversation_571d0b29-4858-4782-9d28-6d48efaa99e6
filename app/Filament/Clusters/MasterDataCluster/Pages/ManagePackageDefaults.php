<?php

namespace App\Filament\Clusters\MasterDataCluster\Pages;

use App\Enums\Currency;
use App\Filament\Clusters\MasterDataCluster;
use App\Settings\PackageDefaults;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Filament\Support\Enums\Alignment;
use Filament\Support\RawJs;

class ManagePackageDefaults extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Package Defaults';

    protected static ?string $title = 'Package Defaults';

    protected static string $settings = PackageDefaults::class;

    protected static ?string $cluster = MasterDataCluster::class;

    protected static ?int $navigationSort = 10;

    public function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Tabs::make('Package Defaults')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Basic Settings')
                            ->schema([
                                Forms\Components\Section::make('Basic Package Settings')
                                    ->schema([
                                        Forms\Components\Grid::make(2)
                                            ->schema([
                                                Forms\Components\TextInput::make('visa')
                                                    ->label('Visa Cost')
                                                    ->numeric()
                                                    ->required()
                                                    ->prefix('SAR')
                                                    ->helperText('Default visa cost per person'),

                                                Forms\Components\TextInput::make('transport')
                                                    ->label('Transport Cost')
                                                    ->numeric()
                                                    ->required()
                                                    ->prefix('SAR')
                                                    ->helperText('Transport cost per person'),

                                                Forms\Components\TextInput::make('transport_min')
                                                    ->label('Minimum Transport Cost')
                                                    ->numeric()
                                                    ->required()
                                                    ->prefix('SAR')
                                                    ->helperText('Minimum total transport cost'),

                                                Forms\Components\TextInput::make('mutawif')
                                                    ->label('Mutawif Cost')
                                                    ->numeric()
                                                    ->required()
                                                    ->prefix('SAR')
                                                    ->helperText('Mutawif cost per night'),

                                                Forms\Components\TextInput::make('margin')
                                                    ->label('Margin')
                                                    ->numeric()
                                                    ->required()
                                                    ->prefix('SAR')
                                                    ->helperText('Default margin per package'),

                                                Forms\Components\TextInput::make('usd_rate')
                                                    ->label('USD Exchange Rate')
                                                    ->numeric()
                                                    ->step(0.01)
                                                    ->required()
                                                    ->helperText('SAR to USD exchange rate'),

                                                Forms\Components\TextInput::make('foc_count')
                                                    ->label('FOC Count')
                                                    ->numeric()
                                                    ->default(0)
                                                    ->helperText('Free of charge count'),

                                                Forms\Components\Select::make('currency')
                                                    ->label('Default Currency')
                                                    ->options(Currency::class)
                                                    ->required(),

                                                Forms\Components\TextInput::make('exchange_rate')
                                                    ->label('Exchange Rate')
                                                    ->numeric()
                                                    ->step(0.01)
                                                    ->default(1.0)
                                                    ->helperText('Exchange rate for currency conversion'),
                                            ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('Costs')
                            ->schema([
                                Forms\Components\Section::make('Costs per Group')
                                    ->schema([
                                        TableRepeater::make('costs')
                                            ->streamlined()
                                            ->hiddenLabel()
                                            ->reorderable(false)
                                            ->addActionLabel('Add cost item')
                                            ->headers([
                                                Header::make('✔')
                                                    ->align(Alignment::Center)
                                                    ->width('1%'),
                                                Header::make('Item'),
                                                Header::make('Price')
                                                    ->width('30%'),
                                            ])
                                            ->schema([
                                                Forms\Components\Checkbox::make('check')
                                                    ->hiddenLabel()
                                                    ->default(true),
                                                Forms\Components\TextInput::make('name')
                                                    ->hiddenLabel()
                                                    ->required(),
                                                Forms\Components\TextInput::make('value')
                                                    ->hiddenLabel()
                                                    ->mask(RawJs::make('$money($input)'))
                                                    ->stripCharacters(',')
                                                    ->required()
                                                    ->numeric()
                                                    ->prefix('SAR'),
                                            ]),
                                    ]),

                                Forms\Components\Section::make('Costs per Pax')
                                    ->schema([
                                        TableRepeater::make('costs_per_pax')
                                            ->streamlined()
                                            ->hiddenLabel()
                                            ->reorderable(false)
                                            ->addActionLabel('Add cost item')
                                            ->headers([
                                                Header::make('✔')
                                                    ->align(Alignment::Center)
                                                    ->width('1%'),
                                                Header::make('Item'),
                                                Header::make('Price')
                                                    ->width('30%'),
                                            ])
                                            ->schema([
                                                Forms\Components\Checkbox::make('check')
                                                    ->hiddenLabel()
                                                    ->default(true),
                                                Forms\Components\TextInput::make('name')
                                                    ->hiddenLabel()
                                                    ->required(),
                                                Forms\Components\TextInput::make('value')
                                                    ->hiddenLabel()
                                                    ->mask(RawJs::make('$money($input)'))
                                                    ->stripCharacters(',')
                                                    ->required()
                                                    ->numeric()
                                                    ->prefix('SAR'),
                                            ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('Package Content')
                            ->schema([
                                Forms\Components\Section::make('Default Pax Options')
                                    ->schema([
                                        Forms\Components\TagsInput::make('meta.paxes')
                                            ->label('Pax Options')
                                            ->helperText('Default passenger count options for packages')
                                            ->placeholder('Add pax count (e.g., 44)')
                                            ->nestedRecursiveRules([
                                                'integer',
                                                'min:1',
                                            ]),
                                    ]),

                                Forms\Components\Section::make('Default Includes')
                                    ->schema([
                                        Forms\Components\Repeater::make('meta.includes')
                                            ->label('Package Includes')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add include item')
                                            ->simple(
                                                Forms\Components\TextInput::make('include')
                                                    ->hiddenLabel()
                                                    ->placeholder('Enter what is included in the package')
                                            ),
                                    ]),

                                Forms\Components\Section::make('Default Excludes')
                                    ->schema([
                                        Forms\Components\Repeater::make('meta.excludes')
                                            ->label('Package Excludes')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add exclude item')
                                            ->simple(
                                                Forms\Components\TextInput::make('exclude')
                                                    ->hiddenLabel()
                                                    ->placeholder('Enter what is excluded from the package')
                                            ),
                                    ]),
                            ]),
                    ]),
            ]);
    }
}
