<?php

namespace App\Filament\Clusters\MasterDataCluster\Resources\CustomerResource\Pages;

use App\Filament\Clusters\MasterDataCluster\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCustomer extends EditRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }
}
