<?php

namespace App\Filament\Resources\PackageResource\Pages;

use App\Filament\Resources\PackageResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewPackage extends ViewRecord
{
    protected static string $resource = PackageResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-bars-3-center-left';

    protected static ?string $navigationLabel = 'Details';

    protected static ?string $breadcrumb = 'Details';

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('download')
                ->color('gray')
                ->action(function ($record) {
                    return pdf()
                        ->view('pdfs.package', ['package' => $record])
                        ->name("package-{$record->id}")
                        ->download();
                })
                ->icon('heroicon-o-arrow-down-tray'),
        ];
    }
}
