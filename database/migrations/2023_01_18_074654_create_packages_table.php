<?php

use App\Models\Package;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();

            $defaults = Package::getDefaults();
            $table->decimal('airport')->default($defaults['airport']);
            $table->decimal('visa')->default($defaults['visa']);
            $table->decimal('transport')->default($defaults['transport']);
            $table->decimal('transport_min')->default($defaults['transport_min']);
            $table->decimal('mutawif')->default($defaults['mutawif']);
            $table->json('costs')->nullable();
            $table->decimal('margin')->default($defaults['margin']);
            $table->decimal('usd_rate')->default($defaults['usd_rate']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('packages');
    }
};
