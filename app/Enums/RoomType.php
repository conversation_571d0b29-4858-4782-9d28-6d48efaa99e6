<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum RoomType: string implements HasLabel
{
    case Single = 'single';
    case Double = 'double';
    case Triple = 'triple';
    case Quad = 'quad';
    case Quint = 'quint';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Single => 'Single',
            self::Double => 'Double',
            self::Triple => 'Triple',
            self::Quad => 'Quad',
            self::Quint => 'Quint',
        };
    }
}
