<?php

namespace App\Filament\Resources\PackageResource\Pages;

use App\Filament\Resources\PackageResource;
use Filament\Actions;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class PreviewPackage extends ViewRecord
{
    protected static string $resource = PackageResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-magnifying-glass';

    protected static ?string $navigationLabel = 'Preview';

    protected static ?string $breadcrumb = 'Preview';

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('download')
                ->color('gray')
                ->action(function ($record) {
                    return pdf()
                        ->view('pdfs.package', ['package' => $record])
                        ->name("package-{$record->id}")
                        ->download();
                })
                ->icon('heroicon-o-arrow-down-tray'),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return 'Preview '.$this->getRecordTitle();
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->extraAttributes([
                'style' => 'overflow-x: auto;',
            ])
            ->schema([
                Infolists\Components\Section::make()
                    ->compact()
                    ->extraAttributes([
                        'style' => 'width: 210mm; min-height: 297mm; padding: 0;',
                        'class' => 'fi-print-preview mx-auto',
                    ])
                    ->schema([
                        Infolists\Components\ViewEntry::make('preview')
                            ->view('components.packages.print', [
                                'package' => $this->getRecord(),
                            ]),
                    ]),
            ]);
    }
}
