<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    protected $connection = 'master';

    protected $fillable = [
        'user_id',

        'name',
        'owner_name',
        'email',
        'phone',
        'logo',
        'logo_square',
        'password',
        'agent_id',

        'permit_no',
        'region', // domisili
        'address',
    ];

    protected $hidden = [
        'password',
    ];

    public function packages(): HasMany
    {
        return $this->hasMany(Package::class);
    }

    public function logoUrl(): Attribute
    {
        return new Attribute(
            get: function () {
                if (blank($this->logo)) {
                    return null;
                }

                return str($this->logo)->startsWith('http') ? $this->logo : config('filesystems.disks.s3.url').$this->logo;
            },
        );
    }

    public function logoSquareUrl(): Attribute
    {
        return new Attribute(
            get: function () {
                if (blank($this->logo_square)) {
                    return null;
                }

                return str($this->logo_square)->startsWith('http') ? $this->logo_square : config('filesystems.disks.s3.url').$this->logo_square;
            },
        );
    }
}
