<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class HotelPackage extends Pivot implements Sortable
{
    use SortableTrait;

    protected $connection = 'mysql';

    public $incrementing = true;

    public $timestamps = false;

    protected $fillable = [
        'order_column',

        'hotel_id',
        'package_id',

        'nights',
        'catering',

        'tax',

        'price_quad',
        'price_triple',
        'price_double',
        'price_single',
    ];

    public function buildSortQuery()
    {
        return static::query()->where('package_id', $this->package_id);
    }

    public function hotel(): BelongsTo
    {
        return $this->belongsTo(Hotel::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }
}
