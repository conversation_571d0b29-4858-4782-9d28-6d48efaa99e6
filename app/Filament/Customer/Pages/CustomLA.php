<?php

namespace App\Filament\Customer\Pages;

use App\Models\Hotel;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Pages\Concerns\HasUnsavedDataChangesAlert;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Guava\FilamentClusters\Forms\Cluster;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class CustomLA extends Page
{
    use HasUnsavedDataChangesAlert;
    use InteractsWithFormActions;

    public ?array $data = [];

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.customer.pages.custom-l-a';

    public function mount(): void
    {
        $this->form->fill();
    }

    public function save(): void
    {
        $data = $this->form->getState();

        dd($data);
    }

    protected function getFormActions(): array
    {
        return [
            // $this->getSubmitFormAction(),
        ];
    }

    protected function getSubmitFormAction(): Action
    {
        return Action::make('save')
            ->submit('save');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make('Informasi Kedatangan')
                        // ->afterValidation(function ($get, $set) {
                        //     $set('arrivals', collect($get('arrivals'))
                        //         ->mapWithKeys(fn ($a, $k) => [$k => $a + ['hotel_id' => null]]));
                        // })
                        ->schema([
                            TableRepeater::make('arrivals')
                                ->label('Kedatangan')
                                ->minItems(1)
                                ->addActionLabel('Tambah')
                                ->default([
                                    ['city' => 'Jeddah'],
                                    ['city' => 'Makkah'],
                                    ['city' => 'Madinah'],
                                ])
                                ->headers([
                                    Header::make('Kota'),
                                    Header::make('Tanggal'),
                                ])
                                ->schema([
                                    Forms\Components\Select::make('city')
                                        ->options([
                                            'Jeddah' => 'Jeddah',
                                            'Makkah' => 'Makkah',
                                            'Madinah' => 'Madinah',
                                        ])
                                        ->required()
                                        ->native(false),
                                    Forms\Components\DatePicker::make('date')
                                        ->required(),
                                    Forms\Components\Hidden::make('hotel_id'),
                                    Forms\Components\Hidden::make('duration'),
                                ]),

                            Cluster::make()
                                ->label('Jumlah Pax')
                                ->columns(3)
                                ->schema([
                                    Forms\Components\TextInput::make('adult_count')
                                        ->prefix('Dewasa')
                                        ->numeric()
                                        ->default('0')
                                        ->minValue(1)
                                        ->required(),
                                    Forms\Components\TextInput::make('child_count')
                                        ->prefix('Anak')
                                        ->numeric()
                                        ->default('0')
                                        ->required(),
                                    Forms\Components\TextInput::make('infant_count')
                                        ->prefix('Bayi')
                                        ->numeric()
                                        ->default('0')
                                        ->required(),
                                ]),
                        ]),
                    Wizard\Step::make('Hotel')
                        ->schema([
                            Forms\Components\Repeater::make('arrivals')
                                ->hiddenLabel()
                                ->reorderable(false)
                                ->deletable(false)
                                ->addable(false)
                                ->itemLabel(fn ($state) => 'Hotel '.$state['city'])
                                ->columns(2)
                                ->schema([
                                    Forms\Components\Hidden::make('city'),
                                    Forms\Components\Select::make('hotel_id')
                                        ->hiddenLabel()
                                        ->columnSpanFull()
                                        ->placeholder(fn ($get) => 'Pilih Hotel '.$get('city'))
                                        ->options(fn ($get) => Hotel::query()
                                            ->where('city', $get('city'))
                                            ->orderBy('name')
                                            ->pluck('name', 'id')
                                        )
                                        ->searchable()
                                        ->required(),
                                    Forms\Components\DatePicker::make('date')
                                        ->hiddenLabel()
                                        ->prefix('Tanggal')
                                        ->extraInputAttributes([
                                            'class' => '!bg-gray-100',
                                        ])
                                        ->readOnly(),
                                    Forms\Components\TextInput::make('duration')
                                        ->hiddenLabel()
                                        ->prefix('Durasi')
                                        ->suffix('malam')
                                        ->numeric()
                                        ->default(0)
                                        ->minValue(1)
                                        ->required(),
                                ]),
                        ]),
                    // ->schema(function ($get) {
                    //     $arrivals = collect($get('arrivals') ?? []);

                    //     return $arrivals
                    //         ->map(
                    //             fn ($a, $i) => Forms\Components\Fieldset::make('Kedatangan '.$a['city'])
                    //                 ->schema([
                    //                     Forms\Components\Select::make("hotels.$i.hotel_id")
                    //                         ->label('Hotel')
                    //                         ->options(
                    //                             Hotel::query()
                    //                                 ->where('city', $a['city'])
                    //                                 ->orderBy('name')
                    //                                 ->pluck('name', 'id')
                    //                         )
                    //                         ->searchable()
                    //                         ->required(),
                    //                 ])
                    //         )
                    //         ->toArray();
                    // }),
                    Wizard\Step::make('Kendaraan')
                        ->schema([
                            Forms\Components\TextInput::make('test'),
                        ]),
                ])->submitAction(new HtmlString(Blade::render(<<<'BLADE'
    <x-filament::button
        type="submit"
        wire:target="save"
    >
        Submit
    </x-filament::button>
BLADE)))
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Ringkasan Pesanan')
                            ->schema([
                                Forms\Components\Placeholder::make('summary_arrivals')
                                    ->content(fn ($get) => json_encode($get('arrivals'), JSON_PRETTY_PRINT)),
                                Forms\Components\Placeholder::make('summary_hotels')
                                    ->content(fn ($get) => json_encode($get('hotels'), JSON_PRETTY_PRINT)),
                            ]),
                    ]),
            ])
            ->statePath('data')
            ->columns(3);
    }
}
