<?php

namespace App\Providers;

use App\Contracts\CurrencyHandler;
use App\Services\CurrencyService;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentColor;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(CurrencyHandler::class, function (Application $app) {
            $apiKey = config('services.currency_api.key');
            $baseUrl = config('services.currency_api.base_url');
            $client = $app->make(\GuzzleHttp\Client::class);

            return new CurrencyService($apiKey, $baseUrl, $client);
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if ($this->app->environment('production')) {
            URL::forceScheme('https');
        }

        \Livewire\Component::macro('toast', function ($message, $type = 'info') {
            /** @var \Livewire\Component $this */
            $this->dispatch('toast', [
                'message' => $message,
                'type' => $type,
            ]);
        });

        FilamentColor::register([
            'primary' => Color::hex('#2e45a2'),
            'secondary' => Color::hex('#c7a34f'),
        ]);
    }
}
