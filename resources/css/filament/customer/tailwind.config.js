import preset from "../../../../vendor/filament/filament/tailwind.config.preset";

/** @type {import('tailwindcss').Config} */
export default {
    presets: [preset],
    content: [
        "./resources/**/*.blade.php",
        "./app/Filament/**/*.php",
        "./resources/views/filament/**/*.blade.php",
        "./vendor/filament/**/*.blade.php",
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./vendor/awcodes/filament-table-repeater/resources/**/*.blade.php",
    ],
    theme: {
        extend: {
            colors: {
                armblue: "rgba(46, 69, 162, <alpha-value>)",
                armyellow: "rgba(199, 163, 79, <alpha-value>)",
            },
        },
    },
};
