@props([
    'placeholder' => 'Pick an item',
    'options' => [],
    'selectedItems' => [],
])

<div
    wire:ignore
    x-data="{
        tom: null,
        options: @js($options),
        items: @js($selectedItems),
        placeholder: @js($placeholder),
        init() {
            this.tom = new TomSelect(this.$refs.input, {
                copyClassesToDropdown: false,
                allowEmptyOption: true,
                dropdownClass: 'dropdown-menu ts-dropdown',
                optionClass: 'dropdown-item',
                controlInput: '<input>',
                options: this.options,
                items: this.items,
                placeholder: this.placeholder,
                render: {
                    item: function(data, escape) {
                        if (data.customProperties) {
                            return `<div><span class='dropdown-item-indicator'>
                                ${data.customProperties}</span>${escape(data.text)}
                                </div>`;
                        }
                        return `<div>${escape(data.text)}</div>`;
                    },
                    option: function(data, escape) {
                        if (data.customProperties) {
                            return `<div><span class='dropdown-item-indicator'>
                                ${data.customProperties}</span>${escape(data.text)}
                                </div>`;
                        }
                        return `<div>${escape(data.text)}</div>`;
                    },
                },
            });
        },
    }"
    >
    <select x-ref="input" {!! $attributes->merge(['class' => 'form-select']) !!}>
        {{ $slot }}
    </select>
</div>
