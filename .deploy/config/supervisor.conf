[supervisord]
logfile=/dev/null
logfile_maxbytes=0
logfile_backups=0
loglevel=info
nodaemon=true

[program:php-fpm]
command=php-fpm --nodaemonize
autorestart=true
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

[program:caddy]
command=caddy run --config %(ENV_LARAVEL_PATH)s/.deploy/config/Caddyfile
autorestart=true
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

[program:cron]
command=crond -l 2 -f
autorestart=true

; [program:horizon]
; command=php %(ENV_LARAVEL_PATH)s/artisan horizon
; autorestart=true
; stdout_events_enabled=true
; stderr_events_enabled=true
; stdout_logfile_maxbytes=0
; stderr_logfile_maxbytes=0
; stdout_logfile=/dev/stdout
; stderr_logfile=/dev/stderr
; stopwaitsecs=3600

; [program:websockets]
; command=php %(ENV_LARAVEL_PATH)s/artisan websockets:serve --port=6001
; numprocs=1
; autorestart=true
; stdout_events_enabled=true
; stderr_events_enabled=true
; stdout_logfile_maxbytes=0
; stderr_logfile_maxbytes=0
; stdout_logfile=/dev/stdout
; stderr_logfile=/dev/stderr
