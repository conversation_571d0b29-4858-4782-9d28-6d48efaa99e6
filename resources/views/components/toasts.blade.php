<div x-data="toasts" @toast.window="add($event.detail)" aria-live="polite" aria-atomic="true">
    <div class="bottom-0 p-3 toast-container position-fixed end-0">
        <template x-for="toast of toasts" :key="toast.id">
            <div :id="'toast-' + toast.id" class="overflow-hidden toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex align-items-center">
                    <div :class="'bg-' + toast.type" class="align-self-stretch" style="width: 4px;"></div>
                    <div class="toast-body" x-html="toast.message"></div>
                    <button type="button" class="m-auto btn-close me-2" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </template>
    </div>
</div>

@push('scripts')
    <script>
        window.toast = function(message, type) {
            window.dispatchEvent(
                new CustomEvent('toast', {
                    detail: {
                        message,
                        type: type || 'info',
                    },
                })
            );
        };

        document.addEventListener('alpine:init', () => {
            Alpine.data('toasts', () => ({
                toasts: [],
                increment: 0,
                add(toast) {
                    toast.id = ++this.increment
                    this.toasts.push(toast)
                    this.$nextTick(() => this.fire(toast.id));
                },
                fire(id) {
                    const el = document.getElementById('toast-' + id);
                    const t = new window.bootstrap.Toast(el);
                    el.addEventListener('hidden.bs.toast', () => {
                        t.dispose();
                    });
                    t.show();
                },
            }));
        });
    </script>
@endpush
