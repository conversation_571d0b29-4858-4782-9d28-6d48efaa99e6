<section class="card">
    <div class="card-body">
        <h3 class="card-title">
            {{ __('Update Password') }}
        </h3>

        <p class="card-subtitle">
            {{ __('Ensure your account is using a long, random password to stay secure.') }}
        </p>

        <form id="update-password" method="post" action="{{ route('password.update') }}">
            @csrf
            @method('put')

            <div class="mb-3">
                <x-input-label for="current_password" :value="__('Current Password')" />
                <x-text-input id="current_password" name="current_password" type="password"
                    autocomplete="current-password" />
                <x-input-error :messages="$errors->updatePassword->get('current_password')" />
            </div>

            <div class="mb-3">
                <x-input-label for="password" :value="__('New Password')" />
                <x-text-input id="password" name="password" type="password"
                    autocomplete="new-password" />
                <x-input-error :messages="$errors->updatePassword->get('password')" />
            </div>

            <div class="mb-3">
                <x-input-label for="password_confirmation" :value="__('Confirm Password')" />
                <x-text-input id="password_confirmation" name="password_confirmation" type="password"
                    autocomplete="new-password" />
                <x-input-error :messages="$errors->updatePassword->get('password_confirmation')" />
            </div>
        </form>
    </div>
    <div class="card-footer">
        <div class="row align-items-center">
            <div class="col">
                @if (session('status') === 'password-updated')
                <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 2000)"
                    class="text-muted">{{ __('Saved.') }}</div>
                @endif
            </div>
            <div class="col-auto">
                <button form="update-password" type="submit" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</section>
