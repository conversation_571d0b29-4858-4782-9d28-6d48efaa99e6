<?php

namespace App\Filament\Resources;

use App\Contracts\CurrencyHandler;
use App\Enums\Currency;
use App\Filament\Resources\PackageResource\Pages;
use App\Models\Customer;
use App\Models\Package;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Fauzie811\FilamentListEntry\Infolists\Components\ListEntry;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\MaxWidth;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

class PackageResource extends Resource
{
    protected static ?string $model = Package::class;

    protected static ?string $navigationIcon = 'heroicon-o-inbox-stack';

    protected static ?string $recordTitleAttribute = 'title';

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->columnSpan(['lg' => 2])
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->label('Package Name')
                                    ->required(),

                                Forms\Components\Select::make('customer_id')
                                    ->label('Customer')
                                    ->relationship('customer', 'name')
                                    ->searchable()
                                    ->preload(),
                            ]),

                        Forms\Components\Tabs::make('Package Details')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('Date Ranges')
                                    ->schema([
                                        TableRepeater::make('dates')
                                            ->streamlined()
                                            ->relationship('dates')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add date range')
                                            ->headers([
                                                Header::make('Date Start'),
                                                Header::make('Date End'),
                                            ])
                                            ->renderHeader(false)
                                            ->schema([
                                                Forms\Components\DatePicker::make('date_start')
                                                    ->required(),
                                                Forms\Components\DatePicker::make('date_end')
                                                    ->required(),
                                            ]),
                                    ]),

                                Forms\Components\Tabs\Tab::make('Hotels')
                                    ->schema([
                                        Forms\Components\Repeater::make('hotel_package')
                                            ->relationship('hotel_package')
                                            ->addActionLabel('Add hotel')
                                            ->columns(1)
                                            ->hiddenLabel()
                                            ->orderColumn('order_column')
                                            ->reorderableWithButtons()
                                            ->schema([
                                                Forms\Components\Grid::make(9)
                                                    ->schema([
                                                        Forms\Components\Select::make('hotel_id')
                                                            ->label('Hotel')
                                                            ->relationship('hotel', 'fullname')
                                                            ->searchable()
                                                            ->preload()
                                                            ->required()
                                                            ->columnSpan(['md' => 4]),
                                                        Forms\Components\TextInput::make('nights')
                                                            ->numeric()
                                                            ->required(),
                                                        Forms\Components\TextInput::make('catering')
                                                            ->numeric()
                                                            ->required()
                                                            ->prefix('SAR', true)
                                                            ->helperText('/ pax/night')
                                                            ->columnSpan(['md' => 2]),
                                                        Forms\Components\TextInput::make('tax')
                                                            ->label('Tax')
                                                            ->numeric()
                                                            ->suffix('%', true)
                                                            ->helperText('/ pax/night')
                                                            ->columnSpan(['md' => 2]),
                                                    ]),
                                                Forms\Components\Grid::make(4)
                                                    ->schema([
                                                        Forms\Components\TextInput::make('price_quad')
                                                            ->label('Price Quad')
                                                            ->numeric()
                                                            ->required()
                                                            ->prefix('SAR', true)
                                                            ->suffix('/ n', true),
                                                        Forms\Components\TextInput::make('price_triple')
                                                            ->label('Price Triple')
                                                            ->numeric()
                                                            ->required()
                                                            ->prefix('SAR', true)
                                                            ->suffix('/ n', true),
                                                        Forms\Components\TextInput::make('price_double')
                                                            ->label('Price Double')
                                                            ->numeric()
                                                            ->required()
                                                            ->prefix('SAR', true)
                                                            ->suffix('/ n', true),
                                                        Forms\Components\TextInput::make('price_single')
                                                            ->label('Price Single')
                                                            ->numeric()
                                                            ->default(0)
                                                            ->prefix('SAR', true)
                                                            ->suffix('/ n', true),
                                                    ]),
                                            ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs::make('Costs')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('Costs per group')
                                    ->schema([
                                        TableRepeater::make('costs')
                                            ->streamlined()
                                            ->hiddenLabel()
                                            ->reorderable(false)
                                            ->addActionLabel('Add item')
                                            ->headers([
                                                Header::make('✔')
                                                    ->align(Alignment::Center)
                                                    ->width('1%'),
                                                Header::make('Item'),
                                                Header::make('Price')
                                                    ->width('30%'),
                                            ])
                                            ->schema([
                                                Forms\Components\Checkbox::make('check')->live(),
                                                Forms\Components\TextInput::make('name')
                                                    ->required(),
                                                Forms\Components\TextInput::make('value')
                                                    ->mask(RawJs::make('$money($input)'))
                                                    ->stripCharacters(',')
                                                    ->required()
                                                    ->numeric()
                                                    ->prefix('SAR', true)
                                                    ->live(),
                                            ]),
                                        Forms\Components\Section::make()
                                            ->columns(1)
                                            ->compact()
                                            ->extraAttributes([
                                                'class' => '!bg-gray-100',
                                            ])
                                            ->schema([
                                                Forms\Components\Placeholder::make('total')
                                                    ->inlineLabel()
                                                    ->content(function ($get) {
                                                        $total = collect($get('costs'))
                                                            ->sum(fn ($item) => $item['check'] ? floatval(str_replace(',', '', $item['value'])) : 0);

                                                        return money($total, 'SAR', true);
                                                    })
                                                    ->extraAttributes([
                                                        'class' => 'text-right font-semibold !text-base',
                                                    ]),
                                            ]),
                                    ]),

                                Forms\Components\Tabs\Tab::make('Costs per pax')
                                    ->schema([
                                        TableRepeater::make('costs_per_pax')
                                            ->streamlined()
                                            ->hiddenLabel()
                                            ->reorderable(false)
                                            ->addActionLabel('Add item')
                                            ->stackAt(MaxWidth::Small)
                                            ->headers([
                                                Header::make('✔')
                                                    ->align(Alignment::Center)
                                                    ->width('1%'),
                                                Header::make('Item'),
                                                Header::make('Price')
                                                    ->width('30%'),
                                            ])
                                            ->schema([
                                                Forms\Components\Checkbox::make('check')
                                                    ->hiddenLabel()
                                                    ->live(),
                                                Forms\Components\TextInput::make('name')
                                                    ->hiddenLabel()
                                                    ->required(),
                                                Forms\Components\TextInput::make('value')
                                                    ->hiddenLabel()
                                                    ->mask(RawJs::make('$money($input)'))
                                                    ->stripCharacters(',')
                                                    ->required()
                                                    ->numeric()
                                                    ->prefix('SAR', true)
                                                    ->live(),
                                            ]),
                                        Forms\Components\Section::make()
                                            ->columns(1)
                                            ->compact()
                                            ->extraAttributes([
                                                'class' => '!bg-gray-100',
                                            ])
                                            ->schema([
                                                Forms\Components\Placeholder::make('total')
                                                    ->inlineLabel()
                                                    ->content(function ($get) {
                                                        $total = collect($get('costs_per_pax'))
                                                            ->sum(fn ($item) => $item['check'] ? floatval(str_replace(',', '', $item['value'])) : 0);

                                                        return money($total, 'SAR', true);
                                                    })
                                                    ->extraAttributes([
                                                        'class' => 'text-right font-semibold !text-base',
                                                    ]),
                                            ]),
                                    ]),
                            ]),

                        Forms\Components\Tabs::make('Includes and Excludes')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('Includes')
                                    ->schema([
                                        Forms\Components\Repeater::make('meta.includes')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add item')
                                            ->default(Package::DEFAULT_INCLUDES)
                                            ->simple(
                                                Forms\Components\TextInput::make('include')
                                            ),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Excludes')
                                    ->schema([
                                        Forms\Components\Repeater::make('meta.excludes')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add item')
                                            ->default(Package::DEFAULT_INCLUDES)
                                            ->simple(
                                                Forms\Components\TextInput::make('exclude')
                                            ),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Notes')
                                    ->schema([
                                        Forms\Components\Repeater::make('meta.notes')
                                            ->hiddenLabel()
                                            ->addActionLabel('Add item')
                                            ->simple(
                                                Forms\Components\TextInput::make('note')
                                            ),
                                    ]),
                            ]),
                    ]),
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\ToggleButtons::make('currency')
                                    ->inline()
                                    ->options(Currency::class)
                                    ->default('USD')
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', app(CurrencyHandler::class)
                                            ->getCachedExchangeRate('USD', $state));
                                    }),
                                Forms\Components\TextInput::make('exchange_rate')
                                    ->label('Exchange Rate')
                                    ->numeric()
                                    ->required()
                                    ->visible(fn ($get) => $get('currency') != 'USD'),
                            ]),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('visa')
                                    ->numeric()
                                    ->prefix('SAR', true)
                                    ->suffix('/ pax', true)
                                    ->required(),
                                Forms\Components\TextInput::make('transport')
                                    ->numeric()
                                    ->prefix('SAR', true)
                                    ->suffix('/ pax', true)
                                    ->required(),
                                Forms\Components\TextInput::make('transport_min')
                                    ->label('Transport Minimum')
                                    ->numeric()
                                    ->prefix('SAR', true)
                                    ->required(),
                                Forms\Components\TextInput::make('mutawif')
                                    ->numeric()
                                    ->prefix('SAR', true)
                                    ->suffix('/ day', true)
                                    ->required(),
                            ]),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('margin')
                                    ->numeric()
                                    ->prefix('SAR', true)
                                    ->required(),
                                Forms\Components\TextInput::make('usd_rate')
                                    ->label('USD Exchange Rate')
                                    ->numeric()
                                    ->prefix('SAR', true)
                                    ->required(),
                            ]),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('foc_count')
                                    ->label('Free of Charge (FOC)')
                                    ->numeric()
                                    ->suffix('pax', true)
                                    ->required(),
                            ]),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\CheckboxList::make('meta.paxes')
                                    ->label('Paxes')
                                    ->options(
                                        collect([44, 40, 36, 32, 28, 24, 20, 16, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1])
                                            ->mapWithKeys(
                                                fn ($i) => [$i => $i.($i > 11 ? ' - '.($i + 3) : '').' pax']
                                            )
                                            ->toArray()
                                    )
                                    ->bulkToggleable()
                                    ->default(Package::DEFAULT_PAXES)
                                    ->required(),
                            ]),
                    ]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('#')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer.name'),
                Tables\Columns\TextColumn::make('title')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('dates')
                    ->badge()
                    ->color('gray')
                    ->listWithLineBreaks()
                    ->getStateUsing(fn ($record) => $record->dates->map(
                        fn ($date) => $date->date_start->format('j M Y').' - '.$date->date_end->format('j M Y')
                    )),
                Tables\Columns\TextColumn::make('hotels.name')
                    ->badge()
                    ->listWithLineBreaks()
                    ->getStateUsing(fn ($record) => $record->hotels->map(
                        fn ($hotel) => $hotel->pivot->nights.' × '.$hotel->fullname
                    )),
                Tables\Columns\TextColumn::make('created_at')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                // Tables\Filters\SelectFilter::make('hotels')
                //     ->relationship('hotels', 'fullname')
                //     ->searchable()
                //     ->preload(),
                Tables\Filters\SelectFilter::make('customer_id')
                    ->label('Customer')
                    ->options(fn () => Customer::query()->orderBy('name')->pluck('name', 'id')->toArray())
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('duplicate')
                        ->icon('heroicon-m-square-2-stack')
                        ->action(fn ($record) => $record->clone())
                        ->requiresConfirmation()
                        ->modalWidth('sm'),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Group::make()
                    ->schema([
                        Section::make('Customer')
                            ->compact()
                            ->schema([
                                Infolists\Components\ImageEntry::make('customer.logoUrl')
                                    ->height('3rem')
                                    ->hiddenLabel(),
                                TextEntry::make('customer.name')
                                    ->hiddenLabel(),
                            ])
                            ->visible(fn ($record) => $record->customer_id !== null),
                        Section::make('Date Ranges')
                            ->compact()
                            ->schema([
                                ListEntry::make('dates')
                                    ->hiddenLabel()
                                    ->itemExtraAttributes([
                                        'class' => 'text-sm',
                                    ])
                                    ->getStateUsing(fn ($record) => $record->dates->map(
                                        fn ($date) => $date->date_start->format('j F Y').' - '.$date->date_end->format('j F Y')
                                    )),
                            ]),
                        Section::make('Hotels')
                            ->compact()
                            ->schema([
                                ListEntry::make('hotels')
                                    ->hiddenLabel()
                                    ->itemExtraAttributes([
                                        'class' => 'text-sm',
                                    ])
                                    ->getStateUsing(function ($record) {
                                        $hotels = $record->hotels->map(
                                            fn ($hotel) => new HtmlString(
                                                $hotel->pivot->nights.' × '.$hotel->fullname.'<br>'.
                                                str_repeat('★', $hotel->stars).'<br>'.
                                                '<strong>Quad:</strong> '.$hotel->pivot->price_quad.'<br>'.
                                                '<strong>Triple:</strong> '.$hotel->pivot->price_triple.'<br>'.
                                                '<strong>Double:</strong> '.$hotel->pivot->price_double.'<br>'.
                                                '<strong>Single:</strong> '.$hotel->pivot->price_single.'<br>'.
                                                '<strong>Catering:</strong> '.$hotel->pivot->catering.'<br>'.
                                                ($hotel->pivot->tax ? '<strong>Tax:</strong> '.$hotel->pivot->tax.'%' : '')
                                            )
                                        );

                                        return $hotels->toArray();
                                    }),
                            ]),
                        Section::make('Package Details')
                            ->compact()
                            ->schema([
                                TextEntry::make('visa')
                                    ->money('SAR')
                                    ->suffix(' / pax'),
                                TextEntry::make('transport')
                                    ->money('SAR')
                                    ->suffix(' / pax'),
                                TextEntry::make('transport_min')
                                    ->label('Transport Minimum')
                                    ->money('SAR'),
                                TextEntry::make('mutawif')
                                    ->money('SAR')
                                    ->suffix(' / day'),
                            ]),
                        Section::make('Costs per group')
                            ->compact()
                            ->schema(function ($record) {
                                $costs = collect($record->costs)
                                    ->filter(fn ($item) => isset($item['check']) ? (bool) $item['check'] : true)
                                    ->map(fn ($item) => TextEntry::make($item['name'])
                                        ->label($item['name'])
                                        ->state($item['value'])
                                        ->money('SAR', true))
                                    ->toArray();

                                return $costs;
                            }),
                        Section::make('Costs per pax')
                            ->compact()
                            ->schema(function ($record) {
                                $costs = collect($record->costs_per_pax)
                                    ->filter(fn ($item) => isset($item['check']) ? (bool) $item['check'] : true)
                                    ->map(fn ($item) => TextEntry::make($item['name'])
                                        ->label($item['name'])
                                        ->state($item['value'])
                                        ->money('SAR', true)
                                        ->suffix(' / pax'))
                                    ->toArray();

                                return $costs;
                            }),
                        Section::make()
                            ->compact()
                            ->schema([
                                TextEntry::make('margin')
                                    ->money('SAR'),
                                TextEntry::make('usd_rate')
                                    ->label('USD Exchange Rate')
                                    ->money('SAR'),
                                TextEntry::make('foc_count')
                                    ->label('Free of Charge (FOC)')
                                    ->suffix(' pax'),
                            ]),
                    ]),
                Infolists\Components\Group::make()
                    ->columnSpan(2)
                    ->schema([
                        ViewEntry::make('price_table')
                            ->view('filament.infolists.components.price-table'),
                        Section::make('Includes')
                            ->compact()
                            ->schema([
                                ListEntry::make('meta.includes')
                                    ->hiddenLabel()
                                    ->state(fn ($record) => $record->meta['includes'] ?? Package::DEFAULT_INCLUDES)
                                    ->itemIcon('heroicon-o-check-circle')
                                    ->itemIconColor('success')
                                    ->itemExtraAttributes([
                                        'class' => 'text-sm',
                                    ]),
                            ]),
                        Section::make('Excludes')
                            ->compact()
                            ->schema([
                                ListEntry::make('meta.excludes')
                                    ->hiddenLabel()
                                    ->state(fn ($record) => $record->meta['excludes'] ?? Package::DEFAULT_EXCLUDES)
                                    ->itemIcon('heroicon-o-x-circle')
                                    ->itemIconColor('danger')
                                    ->itemExtraAttributes([
                                        'class' => 'text-sm',
                                    ]),
                            ]),
                        Section::make('Notes')
                            ->compact()
                            ->schema([
                                ListEntry::make('meta.notes')
                                    ->hiddenLabel()
                                    ->itemIcon('heroicon-o-information-circle')
                                    ->itemIconColor('gray')
                                    ->itemExtraAttributes([
                                        'class' => 'text-sm',
                                    ]),
                            ]),
                    ]),
            ])
            ->columns(3);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPackages::route('/'),
            'create' => Pages\CreatePackage::route('/create'),
            'view' => Pages\ViewPackage::route('/{record}'),
            'edit' => Pages\EditPackage::route('/{record}/edit'),
            'preview' => Pages\PreviewPackage::route('/{record}/preview'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewPackage::class,
            Pages\PreviewPackage::class,
            Pages\EditPackage::class,
        ]);
    }
}
