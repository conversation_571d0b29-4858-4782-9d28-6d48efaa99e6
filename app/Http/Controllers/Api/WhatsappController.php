<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Message;
use App\Services\WhatsApp;
use Illuminate\Http\Request;

class WhatsappController extends Controller
{
    public function sendMessage(Request $request)
    {
        $data = $request->validate([
            'device_id' => ['required', 'exists:devices,id'],
            'phone' => ['required'],
            'message' => ['required'],
        ]);

        $message = Message::create([
            'user_id' => auth()->user()->id,
            'device_id' => $data['device_id'],
            'message_type' => 'outgoing',
            'phone' => formatPhoneNumber($data['phone']),
            'content_type' => 'text',
            'content' => [
                'text' => $data['message'],
            ],
            'status' => 'sending',
        ]);

        if (WhatsApp::sendMessage($message)) {
            $message->update(['status' => 'sent']);
            return response()->json(['success' => true]);
        }

        $message->update(['status' => 'failed']);
        return response()->json(['success' => false]);
    }
}
