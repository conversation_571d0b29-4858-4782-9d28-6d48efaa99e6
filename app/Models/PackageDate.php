<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PackageDate extends Model
{
    protected $connection = 'mysql';

    protected $fillable = [
        'package_id',

        'date_start',
        'date_end',
    ];

    protected $casts = [
        'date_start' => 'date:Y-m-d',
        'date_end' => 'date:Y-m-d',
    ];

    public $timestamps = false;

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }
}
