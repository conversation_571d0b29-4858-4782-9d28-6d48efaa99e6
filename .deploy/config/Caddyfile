:80 {
    root * /srv/app/public

    @websockets {
        header Connection *upgrade*
        header Upgrade    websocket
    }
    reverse_proxy @websockets 127.0.0.1:6001 {
        header_down -X-Powered-By
    }

    redir /index.php / 308
    redir /index.php/ / 308
    route /index.php/* {
        uri strip_prefix /index.php
        redir {path} 308
    }

    php_fastcgi 127.0.0.1:9000
    encode gzip
    header -X-Powered-By
    file_server
    log
}
