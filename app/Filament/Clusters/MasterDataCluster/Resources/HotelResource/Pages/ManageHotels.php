<?php

namespace App\Filament\Clusters\MasterDataCluster\Resources\HotelResource\Pages;

use App\Filament\Clusters\MasterDataCluster\Resources\HotelResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageHotels extends ManageRecords
{
    protected static string $resource = HotelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
