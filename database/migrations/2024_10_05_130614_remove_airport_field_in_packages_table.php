<?php

use App\Models\Package;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Package::query()
            ->where('airport', '>', 0)
            ->get()
            ->each(function ($package) {
                $package->update([
                    'costs_per_pax' => [
                        ...$package->costs_per_pax,
                        [
                            'name' => 'Airport & Snack',
                            'value' => $package->airport,
                            'check' => true,
                        ],
                    ],
                ]);
            });
        Schema::table('packages', function (Blueprint $table) {
            $table->dropColumn('airport');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            //
        });
    }
};
