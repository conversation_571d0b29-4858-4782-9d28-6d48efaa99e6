<?php

namespace App\Filament\Clusters\MasterDataCluster\Resources;

use App\Filament\Clusters\MasterDataCluster;
use App\Filament\Clusters\MasterDataCluster\Resources\HotelResource\Pages;
use App\Models\Hotel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class HotelResource extends Resource
{
    protected static ?string $model = Hotel::class;

    protected static ?string $cluster = MasterDataCluster::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'fullname';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Details')
                    ->schema([
                        Forms\Components\Select::make('city')
                            ->label('City')
                            ->options(Hotel::getCities())
                            ->required(),
                        Forms\Components\TextInput::make('name')
                            ->label('Name')
                            ->required(),
                        Forms\Components\Select::make('stars')
                            ->options([
                                '1' => '★',
                                '2' => '★★',
                                '3' => '★★★',
                                '4' => '★★★★',
                                '5' => '★★★★★',
                            ]),
                        Forms\Components\TextInput::make('distance')
                            ->numeric()
                            ->suffix('m'),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('Base Price')
                    ->schema([
                        Forms\Components\TextInput::make('price_double')
                            ->label('Double')
                            ->numeric()
                            ->prefix('SAR'),
                        Forms\Components\TextInput::make('price_quad')
                            ->label('Quad')
                            ->numeric()
                            ->prefix('SAR'),
                        Forms\Components\TextInput::make('price_triple')
                            ->label('Triple')
                            ->numeric()
                            ->prefix('SAR'),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['lowest_price', 'highest_price']))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->description(fn ($record) => $record->city)
                    ->searchable(['name', 'city'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('distance')
                    ->formatStateUsing(fn ($state) => $state ? $state.'m' : null),
                Tables\Columns\TextColumn::make('stars')
                    ->formatStateUsing(fn ($state) => new HtmlString(
                        '<div class="flex items-center">'.
                        implode('', array_fill(0, $state, Blade::render('<x-heroicon-m-star class="size-4" />'))).
                        '</div>'
                    ))
                    ->sortable()
                    ->color(Color::Amber),
                Tables\Columns\TextColumn::make('price_range')
                    ->getStateUsing(function ($record) {
                        $lowest_price = $record->price_double
                            ? min($record->price_double, $record->lowest_price?->price ?? $record->price_double + 1)
                            : $record->lowest_price?->price ?? 0;
                        $highest_price = max($record->price_quad, $record->highest_price?->price ?? $record->price_quad - 1);

                        return $lowest_price && $highest_price
                            ? money($lowest_price, 'SAR', true).' - '.money($highest_price, 'SAR', true)
                            : null;
                    })
                    ->weight(FontWeight::Medium),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('city')
                    ->options(Hotel::query()->select('city')->distinct()->pluck('city', 'city'))
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\Action::make('prices')
                    ->icon('heroicon-m-currency-dollar')
                    ->button()
                    ->color(Color::Yellow)
                    ->url(fn ($record) => static::getUrl('prices', ['record' => $record])),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageHotels::route('/'),
            'view' => Pages\ViewHotel::route('/{record}'),
            'prices' => Pages\Prices::route('/{record}/prices'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewHotel::class,
            Pages\Prices::class,
        ]);
    }
}
