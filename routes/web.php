<?php

use App\Models\Package;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/login-redirect', fn () => redirect('/login'))
    ->name('login');

Route::middleware(['auth'])->group(function () {
    Route::get('packages/{package}/preview2', function (Package $package) {
        return view('pdfs.package', compact('package'));
    });
});
