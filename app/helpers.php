<?php

use App\Enums\Currency;
use Illuminate\Support\Str;
use Umrahservice\Contracts\PdfService;

function get_gravatar_url($email)
{
    // Trim leading and trailing whitespace from
    // an email address and force all characters
    // to lower case
    $address = strtolower(trim($email));

    // Create an MD5 hash of the final string
    $hash = md5($address);

    // Grab the actual image URL
    return 'https://www.gravatar.com/avatar/'.$hash;
}

function formatDateRange(?DateTime $date1, ?DateTime $date2)
{
    if (! $date1 || ! $date2) {
        return '';
    }
    $date1_fmt = $date1->format('Y') == $date2->format('Y') ? 'd M' : 'd M Y';

    return $date1->format($date1_fmt).' - '.$date2->format('d M Y');
}

function formatPrice($amount, Currency $currency)
{
    return match ($currency) {
        Currency::IDR => '<span class="text-[9pt] align-super">Rp</span> '.number_format(ceil($amount / 1000), 0, ',', '.').'<span class="text-[9pt] align-super">.000</span>',
        Currency::SAR => '<span class="text-[9pt] align-super">SAR</span> '.number_format(round($amount), 0),
        Currency::USD => '<span class="text-[9pt] align-super">$</span> '.number_format(round($amount), 0),
        default => $amount,
    };
}

function formatPhoneNumber($number): string
{
    $number = Str::of(preg_replace('/[^0-9]/', '', $number));

    if ($number->startsWith('0')) {
        return '62'.$number->after('0');
    }

    return $number;
}

function generateRandomToken($length): string
{
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

function getMimeType($filename)
{
    $idx = explode('.', $filename);
    $count_explode = count($idx);
    $idx = strtolower($idx[$count_explode - 1]);

    $mimet = [
        'txt' => 'text/plain',
        'htm' => 'text/html',
        'html' => 'text/html',
        'php' => 'text/html',
        'css' => 'text/css',
        'js' => 'application/javascript',
        'json' => 'application/json',
        'xml' => 'application/xml',
        'swf' => 'application/x-shockwave-flash',
        'flv' => 'video/x-flv',

        // images
        'png' => 'image/png',
        'jpe' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'jpg' => 'image/jpeg',
        'gif' => 'image/gif',
        'bmp' => 'image/bmp',
        'ico' => 'image/vnd.microsoft.icon',
        'tiff' => 'image/tiff',
        'tif' => 'image/tiff',
        'svg' => 'image/svg+xml',
        'svgz' => 'image/svg+xml',

        // archives
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
        'exe' => 'application/x-msdownload',
        'msi' => 'application/x-msdownload',
        'cab' => 'application/vnd.ms-cab-compressed',

        // audio/video
        'mp3' => 'audio/mpeg',
        'qt' => 'video/quicktime',
        'mov' => 'video/quicktime',

        // adobe
        'pdf' => 'application/pdf',
        'psd' => 'image/vnd.adobe.photoshop',
        'ai' => 'application/postscript',
        'eps' => 'application/postscript',
        'ps' => 'application/postscript',

        // ms office
        'doc' => 'application/msword',
        'rtf' => 'application/rtf',
        'xls' => 'application/vnd.ms-excel',
        'ppt' => 'application/vnd.ms-powerpoint',
        'docx' => 'application/msword',
        'xlsx' => 'application/vnd.ms-excel',
        'pptx' => 'application/vnd.ms-powerpoint',

        // open office
        'odt' => 'application/vnd.oasis.opendocument.text',
        'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
    ];

    if (isset($mimet[$idx])) {
        return $mimet[$idx];
    } else {
        return 'application/octet-stream';
    }
}

function pdf(): PdfService
{
    return app(PdfService::class);
}
