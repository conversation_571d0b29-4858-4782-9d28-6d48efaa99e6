<?php

namespace App\Filament\Resources\PackageResource\Pages;

use App\Filament\Resources\PackageResource;
use App\Models\Package;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPackage extends EditRecord
{
    protected static string $resource = PackageResource::class;

    protected static ?string $navigationLabel = 'Edit';

    protected function getHeaderActions(): array
    {
        return [
            $this->getSaveFormAction()
                ->submit(null)
                ->action('save'),
            $this->getCancelFormAction(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['costs'] = collect($data['costs'])
            ->map(fn ($item) => $item + ['check' => isset($item['check']) ? (bool) $item['check'] : true])
            ->toArray();
        $data['costs_per_pax'] = collect($data['costs_per_pax'])
            ->map(fn ($item) => $item + ['check' => isset($item['check']) ? (bool) $item['check'] : true])
            ->toArray();
        $data['meta'] ??= [];
        if (blank($data['meta']['includes'] ?? null)) {
            $data['meta']['includes'] = Package::DEFAULT_INCLUDES;
        }
        if (blank($data['meta']['excludes'] ?? null)) {
            $data['meta']['excludes'] = Package::DEFAULT_EXCLUDES;
        }
        if (blank($data['meta']['paxes'] ?? null)) {
            $data['meta']['paxes'] = Package::DEFAULT_PAXES;
        }
        $notes = $data['meta']['notes'] ?? [];
        if (! is_array($notes)) {
            $notes = json_decode($notes);
            $data['meta']['notes'] = explode("\n", is_array($notes) ? ($notes[0] ?? '') : $notes);
        }

        return $data;
    }
}
