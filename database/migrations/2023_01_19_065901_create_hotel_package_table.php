<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hotel_package', function (Blueprint $table) {
            $table->unsignedBigInteger('hotel_id');
            $table->unsignedBigInteger('package_id');

            $table->unsignedSmallInteger('nights')->default(1);

            $table->decimal('catering', 10)->default(0);

            $table->decimal('price_quad', 10);
            $table->decimal('price_triple', 10);
            $table->decimal('price_double', 10);

            $table->unique(['hotel_id', 'package_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hotel_package');
    }
};
