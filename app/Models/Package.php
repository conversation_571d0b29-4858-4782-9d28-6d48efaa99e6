<?php

namespace App\Models;

use App\Enums\Currency;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Package extends Model
{
    use HasFactory;

    protected $connection = 'mysql';

    const DEFAULT_INCLUDES = [
        'Visa umrah & asuransi kesehatan Arab Saudi.',
        'Akomodasi hotel Madinah & Makkah sesuai program.',
        'Transportasi selama di Arab Saudi.',
        'Transportasi ziarah Makkah 1 x dan Madinah 1x.',
        'Muthawwif atau pembimbing ibadah berbahasa Indonesia.',
        'Mutawwifah raudah atau pembimbing masuk raudah untuk akhwat.',
        'Makan & minum 3x sehari.',
        'Snack premium.',
        'Handling kedatangan dan kepulangan di bandara.',
        'Meal box kedatangan dan kepulangan di bandara.',
        'Handling check-in dan check-out hotel.',
        'Tips porter bandara dan bellboy hotel.',
        "Distribusi koper ke kamar jama'ah.",
        "Air mineral di setiap kamar jama'ah.",
        'Free air zamzam 5lt.',
    ];

    const DEFAULT_EXCLUDES = [
        'Tiket pesawat.',
        'Asuransi perjalanan.',
        'Biaya-biaya yang bersifat pribadi, dan atau yang bukan merupakan fasilitas program.',
        'Biaya tambahan (apabila ada) yang dikeluarkan oleh pemerintah Arab Saudi untuk penerbitan visa umrah.',
    ];

    const DEFAULT_PAXES = [44, 40, 36, 32, 28, 24, 20, 16, 12];

    protected $fillable = [
        'customer_id',

        'title',

        'date_start', // deprecated
        'date_end', // deprecated

        'visa',
        'transport',
        'transport_min',

        'mutawif',

        'costs',
        'costs_per_pax',

        'margin',
        'usd_rate',

        'currency',
        'exchange_rate',

        'foc_count',

        'meta',
    ];

    protected $casts = [
        'date_start' => 'date:Y-m-d',
        'date_end' => 'date:Y-m-d',
        'costs' => 'array',
        'costs_per_pax' => 'array',
        'currency' => Currency::class,
        'meta' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if ($model->meta === null) {
                $model->meta = [
                    'includes' => self::DEFAULT_INCLUDES,
                    'excludes' => self::DEFAULT_EXCLUDES,
                    'paxes' => [44, 40, 36, 32, 28, 24, 20, 16, 12],
                ];
            }
        });
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function dates(): HasMany
    {
        return $this->hasMany(PackageDate::class);
    }

    public function hotels(): BelongsToMany
    {
        $database = $this->getConnection()->getDatabaseName();

        return $this->belongsToMany(Hotel::class, "$database.hotel_package")->using(HotelPackage::class)
            ->withPivot(['order_column', 'nights', 'catering', 'price_quad', 'price_triple', 'price_double', 'price_single', 'tax'])
            ->orderBy('hotel_package.order_column');
    }

    public function hotel_package(): HasMany
    {
        return $this->hasMany(HotelPackage::class)
            ->orderBy('order_column');
    }

    public function isHandlingOnly()
    {
        return blank($this->hotels);
    }

    public function hasPrice($type)
    {
        return ! $this->isHandlingOnly() && $this->hotels[0]->pivot->{"price_$type"} > 0;
    }

    public static function getDefaults(): array
    {
        return [
            'visa' => 550,
            'transport' => 100,
            'transport_min' => 3000,

            'mutawif' => 250,

            'costs' => [
                ['check' => 1, 'name' => 'Baksis Supir', 'value' => 500],
                ['check' => 1, 'name' => 'Baksis Bellboy', 'value' => 500],
                ['check' => 1, 'name' => 'Operasional', 'value' => 1000],
                ['check' => 1, 'name' => 'Mutawifah', 'value' => 200],
                ['check' => 1, 'name' => 'Bus Stasiun', 'value' => 1000],
                ['check' => 1, 'name' => 'Bus Thaif', 'value' => 1000],
                ['check' => 1, 'name' => 'Bus Badar', 'value' => 1500],
                ['check' => 1, 'name' => 'Bus Al-Ula', 'value' => 3000],
                ['check' => 1, 'name' => 'Uang Saku TL', 'value' => 1000],
                ['check' => 1, 'name' => 'Fee TL', 'value' => 1000],
            ],
            'costs_per_pax' => [
                ['check' => 1, 'name' => 'Fee Agen', 'value' => 50],
                ['check' => 1, 'name' => 'Margin Paket Umrah', 'value' => 0],
                ['check' => 1, 'name' => 'Tiket Pesawat', 'value' => 0],
                ['check' => 1, 'name' => 'Tiket Kereta', 'value' => 250],
                ['check' => 1, 'name' => 'Tiket Telefric', 'value' => 100],
                ['check' => 1, 'name' => 'Tiket Toboggan', 'value' => 50],
                ['check' => 1, 'name' => 'Makan Siang Tour', 'value' => 25],
                ['check' => 1, 'name' => 'Snack 3 Trip', 'value' => 20],
                ['check' => 1, 'name' => 'Meal Box PP', 'value' => 40],
                ['check' => 1, 'name' => 'Zamzam 5 ltr', 'value' => 15],
                ['check' => 1, 'name' => 'Handling Bandara PP', 'value' => 80],
                ['check' => 1, 'name' => 'Fee Operator', 'value' => 30],
                ['check' => 1, 'name' => 'Handling Bandara Indonesia', 'value' => 25],
                ['check' => 1, 'name' => 'Lounge Zukavia', 'value' => 50],
                ['check' => 1, 'name' => 'Manasik', 'value' => 50],
                ['check' => 1, 'name' => 'Perlengkapan Umrah', 'value' => 250],
                ['check' => 1, 'name' => 'Siskopatuh', 'value' => 135],
            ],

            'margin' => 3000,
            'usd_rate' => 3.75,

            'foc_count' => 0,

            'meta' => [
                'paxes' => [44, 40, 36, 32, 28, 24, 20, 16, 12],
                'includes' => static::DEFAULT_INCLUDES,
                'excludes' => static::DEFAULT_EXCLUDES,
            ],
        ];
    }

    public function getIncludes()
    {
        return $this->meta['includes'] ?? static::DEFAULT_INCLUDES;
    }

    public function getExcludes()
    {
        return $this->meta['excludes'] ?? static::DEFAULT_EXCLUDES;
    }

    public function getPrices($pax)
    {
        $all_rooms = $pax / 4;

        $hotel_price_quad = 0;
        $hotel_price_triple = 0;
        $hotel_price_double = 0;
        $hotel_price_single = 0;

        $all_rooms_price = 0;

        $total_nights = 0;
        $total_catering = 0;

        foreach ($this->hotels as $hotel) {
            $nights = $hotel->pivot->nights;
            $tax = $hotel->pivot->tax / 100;

            $hotel_price_quad += ($hotel->pivot->price_quad * $nights) * (1 + $tax);
            $hotel_price_triple += ($hotel->pivot->price_triple * $nights) * (1 + $tax);
            $hotel_price_double += ($hotel->pivot->price_double * $nights) * (1 + $tax);
            $hotel_price_single += ($hotel->pivot->price_single * $nights) * (1 + $tax);

            $all_rooms_price += ($hotel->pivot->price_quad * $nights * $all_rooms);

            $total_catering += ($hotel->pivot->catering * $nights);
            $total_nights += $nights;
        }

        $hotel_price_quad /= 4;
        $hotel_price_triple /= 3;
        $hotel_price_double /= 2;

        $pax_price_quad = $this->visa + ($all_rooms_price / $pax) + $total_catering;

        $transport = max($this->transport_min, $this->transport * $pax);
        $mutawif = ($this->mutawif * $total_nights) + $this->mutawif;

        $costs = collect($this->costs)
            ->filter(fn ($item) => $item['check'] ?? true)
            ->sum('value');
        $costs_per_pax = collect($this->costs_per_pax)
            ->filter(fn ($item) => $item['check'] ?? true)
            ->sum('value');

        $pax_costs = ($transport + $mutawif + $costs + $this->margin) / $pax;

        $pax_price_quad += $pax_costs;

        $final_price_quad = ($pax_price_quad + $costs_per_pax) / $this->usd_rate;
        $final_price_triple = ($pax_price_quad + ($hotel_price_triple - $hotel_price_quad) + $costs_per_pax) / $this->usd_rate;
        $final_price_double = ($pax_price_quad + ($hotel_price_double - $hotel_price_quad) + $costs_per_pax) / $this->usd_rate;
        $final_price_single = ($pax_price_quad + ($hotel_price_single - $hotel_price_quad) + $costs_per_pax) / $this->usd_rate;

        if ($this->foc_count) {
            $foc_factor = $this->foc_count / $pax;
            $final_price_quad += $final_price_quad * $foc_factor;
            $final_price_triple += $final_price_triple * $foc_factor;
            $final_price_double += $final_price_double * $foc_factor;
            $final_price_single += $final_price_single * $foc_factor;
        }

        return [
            $final_price_quad,
            $final_price_triple,
            $final_price_double,
            $final_price_single,
        ];
    }

    public function getPriceTable($paxes = [], $formatMoney = true)
    {
        $exchangeRate = $this->currency == Currency::USD ? 1 : $this->exchange_rate;

        if (blank($paxes)) {
            $paxes = $this->meta['paxes'] ?? [44, 40, 36, 32, 28, 24, 20, 16, 12];
        }
        $paxesValue = [];
        collect($paxes)
            ->sortDesc()
            ->mapWithKeys(fn ($v) => [$v => $this->getPrices($v)])
            ->each(function ($value, $key) use (&$paxesValue, $exchangeRate, $formatMoney) {
                $paxes = $key > 11 ? $key.' - '.($key + 3) : $key;
                $paxesValue[] = [
                    'paxes' => $paxes,
                    'price_quad' => $formatMoney
                        ? money(round($value[0] * $exchangeRate), $this->currency->value, true)
                        : round($value[0] * $exchangeRate),
                    'price_triple' => $formatMoney
                        ? money(round($value[1] * $exchangeRate), $this->currency->value, true)
                        : round($value[1] * $exchangeRate),
                    'price_double' => $formatMoney
                        ? money(round($value[2] * $exchangeRate), $this->currency->value, true)
                        : round($value[2] * $exchangeRate),
                    'price_single' => $formatMoney
                        ? money(round($value[3] * $exchangeRate), $this->currency->value, true)
                        : round($value[3] * $exchangeRate),
                ];
            });

        return $paxesValue;
    }

    public function clone(): static
    {
        $clone = $this->replicate();
        $clone->title .= ' - Copy';
        $clone->push();

        foreach ($this->hotel_package as $hotel) {
            $clonedHotel = $hotel->replicate();
            $clone->hotel_package()->save($clonedHotel);
        }

        foreach ($this->dates as $date) {
            $clonedDate = $date->replicate();
            $clone->dates()->save($clonedDate);
        }

        return $clone;
    }

    public function updateMeta($key, $value)
    {
        $meta = $this->meta;
        $meta[$key] = $value;
        $this->meta = $meta;
        $this->save();
    }
}
