@php
    use App\Enums\Currency;
    use App\Contracts\CurrencyHandler;

    $record = $getRecord();
    $paxes = $record->meta['paxes'] ?? [];
    if (blank($paxes)) {
        $paxes = \App\Models\Package::DEFAULT_PAXES;
    }
    $paxes = collect($paxes)->sortDesc()->values()->all();

    $rate = $record->currency == Currency::IDR
        ? $record->exchange_rate
        : app(CurrencyHandler::class)->getCachedExchangeRate('USD', 'IDR');
@endphp
<x-filament-tables::container>
    <x-filament-tables::header
        heading="Price Table"
        actions-position="adaptive"
    />
    <x-filament-tables::table>
        @slot('header')
            <x-filament-tables::header-cell>Total Pax</x-filament-tables::header-cell>
            @if (!$record->isHandlingOnly())
                @if ($record->hasPrice('quad'))
                    <x-filament-tables::header-cell>Quad</x-filament-tables::header-cell>
                @endif
                @if ($record->hasPrice('triple'))
                    <x-filament-tables::header-cell>Triple</x-filament-tables::header-cell>
                @endif
                @if ($record->hasPrice('double'))
                    <x-filament-tables::header-cell>Double</x-filament-tables::header-cell>
                @endif
                @if ($record->hasPrice('single'))
                    <x-filament-tables::header-cell>Single</x-filament-tables::header-cell>
                @endif
            @else
                <x-filament-tables::header-cell>Price per pax</x-filament-tables::header-cell>
            @endif
        @endslot
        @foreach ($paxes as $index => $pax)
            <x-filament-tables::row :striped="$index % 2 !== 0">
                <x-filament-tables::cell>
                    <div class="px-3 py-3 text-sm">
                        {{ $pax . ($pax > 11 ? ' - ' . ($pax + 3) : '') }} pax
                    </div>
                </x-filament-tables::cell>
                @php
                    $prices = $record->getPrices($pax);
                @endphp
                @if ($record->hasPrice('quad') || $record->isHandlingOnly())
                    <x-filament-tables::cell>
                        <div class="px-3 py-3 text-sm">
                            {{ money(round($prices[0]), 'USD', true) }}
                            <p class="text-xs text-gray-500">{{ money(round($prices[0] * $rate), 'IDR', true) }}</p>
                        </div>
                    </x-filament-tables::cell>
                @endif
                @if ($record->hasPrice('triple'))
                    <x-filament-tables::cell>
                        <div class="px-3 py-3 text-sm">
                            {{ money(round($prices[1]), 'USD', true) }}
                            <p class="text-xs text-gray-500">{{ money(round($prices[1] * $rate), 'IDR', true) }}</p>
                        </div>
                    </x-filament-tables::cell>
                @endif
                @if ($record->hasPrice('double'))
                    <x-filament-tables::cell>
                        <div class="px-3 py-3 text-sm">
                            {{ money(round($prices[2]), 'USD', true) }}
                            <p class="text-xs text-gray-500">{{ money(round($prices[2] * $rate), 'IDR', true) }}</p>
                        </div>
                    </x-filament-tables::cell>
                @endif
                @if ($record->hasPrice('single'))
                    <x-filament-tables::cell>
                        <div class="px-3 py-3 text-sm">
                            {{ money(round($prices[3]), 'USD', true) }}
                            <p class="text-xs text-gray-500">{{ money(round($prices[3] * $rate), 'IDR', true) }}</p>
                        </div>
                    </x-filament-tables::cell>
                @endif
            </x-filament-tables::row>
        @endforeach
    </x-filament-tables::table>
</x-filament-tables::container>
