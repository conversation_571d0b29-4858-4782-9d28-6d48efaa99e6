<?php

namespace App\Filament\Resources\PackageResource\Pages;

use App\Filament\Resources\PackageResource;
use App\Models\Package;
use Filament\Resources\Pages\CreateRecord;

class CreatePackage extends CreateRecord
{
    protected static string $resource = PackageResource::class;

    protected function fillForm(): void
    {
        $this->callHook('beforeFill');

        $this->form->fill(Package::getDefaults());

        $this->callHook('afterFill');
    }

    protected function getHeaderActions(): array
    {
        return [
            $this->getCreateFormAction()
                ->submit(null)
                ->action('create'),
            $this->getCancelFormAction(),
        ];
    }
}
