<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, Notifiable;
    use HasRoles;

    protected $connection = 'master';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'user' => 'hashed',
    ];

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    public function isSuperAdmin(): bool
    {
        return $this->id === 1;
    }

    public function rememberTokens(): HasMany
    {
        return $this->hasMany(RememberToken::class);
    }

    public function getRememberToken()
    {
        $host = request()->host();

        return $this->rememberTokens()->where('host', $host)->value('token');
    }

    public function setRememberToken($value)
    {
        $host = request()->host();
        $this->rememberTokens()->updateOrCreate([
            'host' => $host,
        ], [
            'token' => $value,
        ]);
    }
}
